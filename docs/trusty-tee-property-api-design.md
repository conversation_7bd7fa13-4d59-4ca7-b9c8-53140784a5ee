# Trusty TEE GP属性API完整设计文档 - 参考OP-TEE属性组织

## 1. 概述

### 1.1 设计目标

本文档参考OP-TEE的属性组织方式和枚举机制，为Trusty TEE设计完整的GP标准属性API实现方案，包括：

- 完整的12个GP标准属性API
- 保留TIPC通用TA服务获取内核属性的机制
- 参考OP-TEE的属性组织结构和枚举方式
- 与OP-TEE属性位置方式保持一致
- 不考虑manifest属性，专注于属性获取方案

### 1.2 OP-TEE属性组织参考

**OP-TEE属性系统特点：**
- 三类属性集：TEE_IMPLEMENTATION、CURRENT_TA、CURRENT_CLIENT
- 静态属性数组：tee_props[]、ta_props[]
- 属性枚举器：基于索引的遍历机制
- 属性类型：BOOL、U32、U64、STRING、UUID、IDENTITY、BINARY_BLOCK

**Trusty TEE适配策略：**
- 保持OP-TEE的属性分类和组织方式
- 通过TIPC服务访问内核中的属性数据
- 实现与OP-TEE兼容的枚举器机制
- 保留用户空间的属性缓存机制

### 1.3 GP标准属性API列表

**基础属性获取API（7个）：**
1. `TEE_GetPropertyAsString` - 获取字符串属性
2. `TEE_GetPropertyAsBool` - 获取布尔属性
3. `TEE_GetPropertyAsU32` - 获取32位整数属性
4. `TEE_GetPropertyAsU64` - 获取64位整数属性
5. `TEE_GetPropertyAsBinaryBlock` - 获取二进制块属性
6. `TEE_GetPropertyAsUUID` - 获取UUID属性
7. `TEE_GetPropertyAsIdentity` - 获取身份属性

**属性枚举API（5个）：**
8. `TEE_AllocatePropertyEnumerator` - 分配属性枚举器
9. `TEE_FreePropertyEnumerator` - 释放属性枚举器
10. `TEE_StartPropertyEnumerator` - 启动属性枚举
11. `TEE_ResetPropertyEnumerator` - 重置属性枚举器
12. `TEE_GetPropertyName` - 获取属性名称
13. `TEE_GetNextProperty` - 获取下一个属性

## 2. 基于OP-TEE属性组织的架构设计

### 2.1 OP-TEE vs Trusty TEE属性组织对比

**OP-TEE属性组织：**
```
用户空间 (libutee):
  - 本地静态属性数组 (tee_props[], ta_props[])
  - 属性枚举器 (prop_enumerator)
  ↓ 系统调用 (缺失时)
内核空间 (core/tee):
  - 动态属性查找
  - 枚举器状态管理
```

**Trusty TEE适配组织：**
```
用户空间 (libutee):
  - 本地静态属性数组 (tee_props[], ta_props[])
  - 属性枚举器 (prop_enumerator)
  ↓ TIPC通用TA服务 (缺失时)
内核空间 (kernel/rctee):
  - 通用TA服务处理属性请求
  - 内核属性数据库
```

### 2.2 OP-TEE属性组织结构参考

#### 2.2.1 OP-TEE属性分类

**TEE实现属性 (tee_props[])：**
- `gpd.tee.arith.maxBigIntSize` - 大整数最大位数
- `gpd.tee.sockets.version` - Socket版本
- `gpd.tee.sockets.tcp.version` - TCP Socket版本
- `gpd.tee.internalCore.version` - 内核API版本

**TA属性 (ta_props[])：**
- TA特定的属性（由TA manifest定义）
- 运行时动态属性

**客户端属性：**
- 客户端身份相关属性
- 通常为空或动态获取

#### 2.2.2 属性类型枚举（与OP-TEE保持一致）

```c
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,              /* bool */
    USER_TA_PROP_TYPE_U32,               /* uint32_t */
    USER_TA_PROP_TYPE_STRING,            /* zero terminated string of char */
    USER_TA_PROP_TYPE_U64,               /* uint64_t */
    USER_TA_PROP_TYPE_UUID,              /* TEE_UUID */
    USER_TA_PROP_TYPE_IDENTITY,          /* TEE_Identity */
    USER_TA_PROP_TYPE_BINARY_BLOCK,      /* binary block */
    USER_TA_PROP_TYPE_INVALID,           /* invalid value */
};
```

#### 2.2.3 属性结构定义（与OP-TEE保持一致）

```c
struct user_ta_property {
    const char* name;                    /* 属性名称 */
    enum user_ta_prop_type type;         /* 属性类型 */
    const void* value;                   /* 属性值指针 */
};
```

#### 2.2.4 属性枚举器结构（参考OP-TEE）

```c
#define PROP_ENUMERATOR_NOT_STARTED     0xffffffff

struct prop_enumerator {
    uint32_t idx;                        /* 当前索引 */
    TEE_PropSetHandle prop_set;          /* 属性集句柄 */
};
```

#### 2.2.5 Trusty TEE属性数组定义

```c
/* TEE实现属性 - 参考OP-TEE的tee_props[] */
const struct user_ta_property tee_props[] = {
    {
        "gpd.tee.arith.maxBigIntSize",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){CFG_TA_BIGNUM_MAX_BITS}
    },
    {
        "gpd.tee.systemTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){100}  /* REE级别保护 */
    },
    {
        "gpd.tee.TAPersistentTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){1000} /* TEE硬件级别保护 */
    },
    {
        "gpd.tee.internalCore.version",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){TEE_CORE_API_VERSION}
    }
};

/* TA属性 - 由TA定义，参考OP-TEE的ta_props[] */
extern const struct user_ta_property ta_props[];
extern const size_t ta_num_props;
```

### 2.3 TIPC通用TA服务协议设计

#### 2.3.1 属性查找策略（参考OP-TEE）

```c
/* 参考OP-TEE的propset_get函数 */
static TEE_Result propset_get(TEE_PropSetHandle h,
                              const struct user_ta_property** eps,
                              size_t* eps_len) {
    if (h == TEE_PROPSET_CURRENT_TA) {
        *eps = ta_props;
        *eps_len = ta_num_props;
    } else if (h == TEE_PROPSET_CURRENT_CLIENT) {
        *eps = NULL;  /* 客户端属性通过TIPC获取 */
        *eps_len = 0;
    } else if (h == TEE_PROPSET_TEE_IMPLEMENTATION) {
        *eps = tee_props;
        *eps_len = ARRAY_SIZE(tee_props);
    } else {
        return TEE_ERROR_ITEM_NOT_FOUND;
    }
    return TEE_SUCCESS;
}
```

#### 2.3.2 TIPC属性请求协议

```c
/* 属性查找请求 */
struct property_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    uint32_t prop_set;                   /* 属性集句柄 */
    union {
        struct {
            char name[64];               /* 按名称查找 */
        } by_name;
        struct {
            uint32_t index;              /* 按索引查找 */
        } by_index;
    };
};

/* 属性响应 */
struct property_response {
    int32_t result;                      /* 操作结果 */
    uint32_t prop_type;                  /* 属性类型 */
    char name[64];                       /* 属性名称 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
        uint8_t binary_value[256];       /* 二进制值 */
        TEE_UUID uuid_value;             /* UUID值 */
        TEE_Identity identity_value;     /* 身份值 */
    };
    uint32_t value_len;                  /* 值长度 */
};

/* 枚举请求 */
struct property_enum_request {
    struct generic_ta_msg_hdr hdr;       /* 消息头 */
    uint32_t prop_set;                   /* 属性集句柄 */
    uint32_t start_index;                /* 起始索引 */
    uint32_t count;                      /* 请求数量 */
};

/* 枚举响应 */
struct property_enum_response {
    int32_t result;                      /* 操作结果 */
    uint32_t total_count;                /* 总属性数量 */
    uint32_t returned_count;             /* 返回的属性数量 */
    struct {
        char name[64];                   /* 属性名称 */
        uint32_t type;                   /* 属性类型 */
    } properties[16];                    /* 属性列表 */
};
```

#### 2.3.3 TIPC命令类型

```c
enum generic_ta_cmd {
    GENERIC_TA_CMD_INVALID = 0,
    /* 属性获取命令 */
    GENERIC_TA_CMD_GET_PROPERTY_BY_NAME = 1,
    GENERIC_TA_CMD_GET_PROPERTY_BY_INDEX = 2,
    /* 属性枚举命令 */
    GENERIC_TA_CMD_ENUM_PROPERTIES = 3,
    GENERIC_TA_CMD_GET_PROPERTY_COUNT = 4,
    /* 兼容现有命令 */
    GENERIC_TA_CMD_GET_PROPERTY_STRING = 10,
    GENERIC_TA_CMD_GET_PROPERTY_BOOL = 11,
    GENERIC_TA_CMD_GET_PROPERTY_U32 = 12,
    GENERIC_TA_CMD_GET_PROPERTY_U64 = 13,
    GENERIC_TA_CMD_GET_PROPERTY_BINARY = 14,
    GENERIC_TA_CMD_GET_PROPERTY_UUID = 15,
    GENERIC_TA_CMD_GET_PROPERTY_IDENTITY = 16,
};
```

### 2.4 已实现API分析

#### 2.4.1 TEE_GetPropertyAsString

**输入**：属性集句柄、属性名称、值缓冲区、缓冲区长度指针
**输出**：字符串值、实际长度
**返回值**：TEE_Result错误码
**说明**：支持类型转换，可将BOOL和U32类型转换为字符串

#### 2.4.2 TEE_GetPropertyAsBool

**输入**：属性集句柄、属性名称、值指针
**输出**：布尔值
**返回值**：TEE_Result错误码
**说明**：严格类型检查，只接受BOOL类型属性

#### 2.4.3 TEE_GetPropertyAsU32

**输入**：属性集句柄、属性名称、值指针
**输出**：32位无符号整数值
**返回值**：TEE_Result错误码
**说明**：严格类型检查，只接受U32类型属性

#### 2.4.4 TEE_GetPropertyAsU64

**输入**：属性集句柄、属性名称、值指针
**输出**：64位无符号整数值
**返回值**：TEE_Result错误码
**说明**：严格类型检查，只接受U64类型属性

## 3. 系统架构设计（基于OP-TEE属性组织 + TIPC服务）

### 3.1 总体架构图

```mermaid
graph TB
    subgraph Layer1 ["第一层: GP API层"]
        A["TEE_GetPropertyAsString<br/>TEE_GetPropertyAsBool<br/>TEE_GetPropertyAsU32<br/>TEE_GetPropertyAsU64<br/>TEE_GetPropertyAsBinaryBlock<br/>TEE_GetPropertyAsUUID<br/>TEE_GetPropertyAsIdentity<br/><br/>TEE_AllocatePropertyEnumerator<br/>TEE_FreePropertyEnumerator<br/>TEE_StartPropertyEnumerator<br/>TEE_ResetPropertyEnumerator<br/>TEE_GetPropertyName<br/>TEE_GetNextProperty"]
    end

    subgraph Layer2 ["第二层: 本地属性获取层"]
        B["propget_get_property 核心属性获取<br/>propset_get 属性集管理<br/>propget_get_ext_prop 值提取<br/><br/>tee_props数组 TEE实现属性<br/>ta_props数组 TA属性<br/>client_props数组 客户端属性<br/><br/>属性枚举器管理"]
    end

    subgraph Layer3 ["第三层: TIPC通用TA服务层"]
        C["TIPC连接和消息收发<br/><br/>内核属性数据库查找<br/>kernel_tee_props数组<br/><br/>动态属性生成<br/>客户端身份属性<br/>运行时属性"]
    end

    Layer1 ==> Layer2
    Layer2 ==> Layer3

    style Layer1 fill:#e1f5fe,stroke:#01579b,stroke-width:4px
    style Layer2 fill:#f3e5f5,stroke:#4a148c,stroke-width:4px
    style Layer3 fill:#e8f5e8,stroke:#1b5e20,stroke-width:4px
```

### 3.2 三层架构说明

#### 🔵 第一层：GP API层
- **功能**：提供完整的12个GP标准属性API
- **位置**：用户空间 (lib/libutee)
- **重点**：标准接口，符合GP规范

#### 🟣 第二层：本地属性获取层
- **功能**：OP-TEE风格的属性组织和本地查找
- **位置**：用户空间 (lib/libutee)
- **重点**：本地优先，高性能查找

#### 🟢 第三层：TIPC通用TA服务层
- **功能**：通过TIPC服务获取内核属性
- **位置**：内核空间 (通用TA服务)
- **重点**：动态扩展，内核属性支持

### 3.3 数据流向

**查找流程**：GP API → 本地属性查找 → TIPC服务(如需要) → 返回结果

**设计优势**：
- 清晰分层，职责明确
- 本地优先，性能优化
- OP-TEE兼容，便于移植
- TIPC扩展，支持动态属性



### 3.2 属性获取流程图

```mermaid
graph TB
    Start([开始]) --> CheckHandle{检查属性集句柄}
    CheckHandle -->|伪句柄| LocalSearch[本地属性搜索]
    CheckHandle -->|枚举器句柄| EnumSearch[枚举器属性搜索]

    LocalSearch --> Found{找到属性?}
    Found -->|是| TypeConvert[类型转换]
    Found -->|否| RemoteSearch[远程服务搜索]

    EnumSearch --> EnumFound{枚举器中找到?}
    EnumFound -->|是| TypeConvert
    EnumFound -->|否| Error[返回错误]

    RemoteSearch --> Connect[连接Generic TA服务]
    Connect --> SendReq[发送属性请求]
    SendReq --> WaitResp[等待响应]
    WaitResp --> ParseResp[解析响应]
    ParseResp --> TypeConvert

    TypeConvert --> Success[返回成功]
    Error --> End([结束])
    Success --> End
```

### 3.3 属性枚举流程图

```mermaid
graph TB
    Start([开始枚举]) --> Alloc[分配枚举器]
    Alloc --> StartEnum[启动枚举器]
    StartEnum --> SetPropSet[设置属性集]
    SetPropSet --> InitIndex[初始化索引]

    InitIndex --> GetNext[获取下一个属性]
    GetNext --> HasMore{还有属性?}
    HasMore -->|是| ReturnProp[返回属性信息]
    HasMore -->|否| EndEnum[结束枚举]

    ReturnProp --> GetNext
    EndEnum --> FreeEnum[释放枚举器]
    FreeEnum --> End([结束])
```

## 4. 实现方案（参考OP-TEE属性组织 + TIPC服务）

### 4.1 用户空间API实现（lib/libutee/tee_api_property.c）

#### 4.1.1 核心属性获取函数（参考OP-TEE + TIPC扩展）

```c
/* 参考OP-TEE的propget_get_property函数，但使用TIPC替代系统调用 */
static TEE_Result propget_get_property(TEE_PropSetHandle h, const char* name,
                                       enum user_ta_prop_type* type,
                                       void* buf, uint32_t* len) {
    TEE_Result res;
    const struct user_ta_property* eps;
    size_t eps_len;
    size_t n;

    if (is_propset_pseudo_handle(h)) {
        /* 首先在本地静态属性中查找 - 参考OP-TEE */
        res = propset_get(h, &eps, &eps_len);
        if (res != TEE_SUCCESS)
            return res;

        for (n = 0; n < eps_len; n++) {
            if (!strcmp(name, eps[n].name))
                return propget_get_ext_prop(eps + n, type, buf, len);
        }

        /* 本地未找到，通过TIPC服务查找 */
        return get_property_from_tipc_service(h, name, type, buf, len);
    } else {
        /* 枚举器句柄处理 - 参考OP-TEE */
        struct prop_enumerator* pe = (struct prop_enumerator*)h;
        uint32_t idx = pe->idx;

        if (idx == PROP_ENUMERATOR_NOT_STARTED)
            return TEE_ERROR_ITEM_NOT_FOUND;

        res = propset_get(pe->prop_set, &eps, &eps_len);
        if (res != TEE_SUCCESS)
            return res;

        if (idx < eps_len)
            return propget_get_ext_prop(eps + idx, type, buf, len);

        /* 超出本地范围，通过TIPC服务获取 */
        idx -= eps_len;
        return get_property_by_index_from_tipc_service(pe->prop_set, idx,
                                                       name, type, buf, len);
    }
}

/* TIPC服务属性获取函数 */
static TEE_Result get_property_from_tipc_service(TEE_PropSetHandle prop_set,
                                                  const char* name,
                                                  enum user_ta_prop_type* type,
                                                  void* buf, uint32_t* len) {
    handle_t chan;
    int rc = tipc_connect(&chan, GENERIC_TA_PORT);
    if (rc < 0) {
        TLOGE("Failed to connect to service (%d)\n", rc);
        return TEE_ERROR_COMMUNICATION;
    }

    struct property_request req;
    memset(&req, 0, sizeof(req));
    req.hdr.cmd = GENERIC_TA_CMD_GET_PROPERTY_BY_NAME;
    req.prop_set = (uint32_t)prop_set;
    strncpy(req.by_name.name, name, sizeof(req.by_name.name) - 1);

    /* 发送请求 */
    rc = tipc_send1(chan, &req, sizeof(req));
    if (rc < 0) {
        close(chan);
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    struct property_response resp;
    rc = property_read(chan, sizeof(resp), &resp, sizeof(resp));
    close(chan);

    if (rc < 0)
        return TEE_ERROR_COMMUNICATION;

    if (resp.result != NO_ERROR)
        return sys_to_tee_error(resp.result);

    /* 复制属性值 */
    return copy_property_value_to_buffer(&resp, type, buf, len);
}
```

#### 4.1.2 属性枚举器实现（参考OP-TEE）

```c
/* TEE_AllocatePropertyEnumerator - 参考OP-TEE实现 */
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropSetHandle* enumerator) {
    struct prop_enumerator* pe;

    if (!enumerator)
        return TEE_ERROR_BAD_PARAMETERS;

    pe = TEE_Malloc(sizeof(struct prop_enumerator),
                    TEE_USER_MEM_HINT_NO_FILL_ZERO);
    if (!pe)
        return TEE_ERROR_OUT_OF_MEMORY;

    *enumerator = (TEE_PropSetHandle)pe;
    TEE_ResetPropertyEnumerator(*enumerator);
    return TEE_SUCCESS;
}

/* TEE_FreePropertyEnumerator - 参考OP-TEE实现 */
void TEE_FreePropertyEnumerator(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    TEE_Free(pe);
}

/* TEE_StartPropertyEnumerator - 参考OP-TEE实现 */
void TEE_StartPropertyEnumerator(TEE_PropSetHandle enumerator,
                                 TEE_PropSetHandle propSet) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;

    if (!pe)
        return;

    pe->idx = 0;
    pe->prop_set = propSet;
}

/* TEE_ResetPropertyEnumerator - 参考OP-TEE实现 */
void TEE_ResetPropertyEnumerator(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    pe->idx = PROP_ENUMERATOR_NOT_STARTED;
}

/* TEE_GetPropertyName - 参考OP-TEE实现 */
TEE_Result TEE_GetPropertyName(TEE_PropSetHandle enumerator,
                               void* nameBuffer, size_t* nameBufferLen) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    const struct user_ta_property* eps;
    size_t eps_len;
    const char* str;
    size_t bufferlen;
    TEE_Result res;

    if (!pe)
        return TEE_ERROR_BAD_PARAMETERS;

    bufferlen = *nameBufferLen;
    res = propset_get(pe->prop_set, &eps, &eps_len);
    if (res != TEE_SUCCESS)
        return res;

    if (pe->idx < eps_len) {
        /* 本地属性 */
        str = eps[pe->idx].name;
        bufferlen = strlcpy(nameBuffer, str, *nameBufferLen) + 1;
        if (bufferlen > *nameBufferLen)
            res = TEE_ERROR_SHORT_BUFFER;
        *nameBufferLen = bufferlen;
    } else {
        /* TIPC服务属性 */
        res = get_property_name_from_tipc_service(pe->prop_set,
                                                  pe->idx - eps_len,
                                                  nameBuffer, nameBufferLen);
    }

    return res;
}

/* TEE_GetNextProperty - 参考OP-TEE实现 */
TEE_Result TEE_GetNextProperty(TEE_PropSetHandle enumerator) {
    struct prop_enumerator* pe = (struct prop_enumerator*)enumerator;
    const struct user_ta_property* eps;
    size_t eps_len;
    uint32_t next_idx;
    TEE_Result res;

    if (!pe)
        return TEE_ERROR_BAD_PARAMETERS;

    if (pe->idx == PROP_ENUMERATOR_NOT_STARTED)
        return TEE_ERROR_ITEM_NOT_FOUND;

    res = propset_get(pe->prop_set, &eps, &eps_len);
    if (res != TEE_SUCCESS)
        return res;

    next_idx = pe->idx + 1;
    pe->idx = next_idx;

    if (next_idx < eps_len) {
        /* 还在本地属性范围内 */
        return TEE_SUCCESS;
    } else {
        /* 检查TIPC服务是否还有更多属性 */
        return check_tipc_property_exists(pe->prop_set, next_idx - eps_len);
    }
}
```

## 5. 属性枚举器设计

### 5.1 枚举器数据结构

#### 5.1.1 属性枚举器结构

```c
struct property_enumerator {
    struct list_node link;               /* 枚举器链表节点 */
    TEE_PropSetHandle propset;           /* 关联的属性集 */
    size_t current_index;                /* 当前枚举索引 */
    bool started;                        /* 是否已启动枚举 */
    const struct user_ta_property* props; /* 属性数组指针 */
    size_t props_count;                  /* 属性数组大小 */
    mutex_t enum_lock;                   /* 枚举器锁 */
};
```

#### 5.1.2 枚举器句柄类型

```c
typedef struct property_enumerator* TEE_PropertyEnumeratorHandle;
```

### 5.2 枚举器管理API

#### 5.2.1 TEE_AllocatePropertyEnumerator

```c
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropertyEnumeratorHandle* enumerator);
```

**输入**：枚举器句柄指针
**输出**：分配的枚举器句柄
**返回值**：TEE_Result错误码
**说明**：分配一个新的属性枚举器，初始化为未启动状态

**实现要点**：
- 分配枚举器内存
- 初始化链表节点和锁
- 设置初始状态为未启动

#### 5.2.2 TEE_FreePropertyEnumerator

```c
void TEE_FreePropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator);
```

**输入**：枚举器句柄
**输出**：无
**返回值**：无
**说明**：释放属性枚举器及其占用的资源

**实现要点**：
- 检查枚举器有效性
- 清理锁资源
- 释放内存

#### 5.2.3 TEE_StartPropertyEnumerator

```c
TEE_Result TEE_StartPropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator,
                                       TEE_PropSetHandle propSet);
```

**输入**：枚举器句柄、属性集句柄
**输出**：无
**返回值**：TEE_Result错误码
**说明**：启动属性枚举器，关联到指定的属性集

**实现要点**：
- 验证属性集句柄有效性
- 获取属性集对应的属性数组
- 重置枚举索引为0
- 设置启动状态

#### 5.2.4 TEE_ResetPropertyEnumerator

```c
void TEE_ResetPropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator);
```

**输入**：枚举器句柄
**输出**：无
**返回值**：无
**说明**：重置属性枚举器到初始状态

**实现要点**：
- 重置枚举索引为0
- 保持属性集关联
- 保持启动状态

#### 5.2.5 TEE_GetPropertyName

```c
TEE_Result TEE_GetPropertyName(TEE_PropertyEnumeratorHandle enumerator,
                               void* nameBuffer,
                               size_t* nameBufferLen);
```

**输入**：枚举器句柄、名称缓冲区、缓冲区长度指针
**输出**：属性名称、实际长度
**返回值**：TEE_Result错误码
**说明**：获取当前枚举位置的属性名称

**实现要点**：
- 检查枚举器状态
- 验证当前索引有效性
- 复制属性名称到缓冲区
- 处理缓冲区长度不足情况

#### 5.2.6 TEE_GetNextProperty

```c
TEE_Result TEE_GetNextProperty(TEE_PropertyEnumeratorHandle enumerator);
```

**输入**：枚举器句柄
**输出**：无
**返回值**：TEE_Result错误码
**说明**：移动枚举器到下一个属性

**实现要点**：
- 检查枚举器状态
- 递增枚举索引
- 检查是否超出属性数组范围
- 返回相应的结果码

## 6. 扩展数据类型支持

### 6.1 新增属性类型

#### 6.1.1 二进制块类型

```c
#define USER_TA_PROP_TYPE_BINARY    4    /* binary block */

struct binary_property {
    const uint8_t* data;                 /* 二进制数据指针 */
    size_t length;                       /* 数据长度 */
};
```

#### 6.1.2 UUID类型

```c
#define USER_TA_PROP_TYPE_UUID      5    /* UUID */

typedef struct {
    uint32_t timeLow;
    uint16_t timeMid;
    uint16_t timeHiAndVersion;
    uint8_t clockSeqAndNode[8];
} TEE_UUID;
```

#### 6.1.3 身份类型

```c
#define USER_TA_PROP_TYPE_IDENTITY  6    /* Identity */

typedef struct {
    uint32_t login;                      /* 登录类型 */
    TEE_UUID uuid;                       /* 身份UUID */
} TEE_Identity;
```

### 6.2 扩展通信协议

#### 6.2.1 新增命令类型

```c
enum generic_ta_cmd {
    GENERIC_TA_CMD_INVALID = 0,
    GENERIC_TA_CMD_GET_PROPERTY_STRING = 1,
    GENERIC_TA_CMD_GET_PROPERTY_BOOL = 2,
    GENERIC_TA_CMD_GET_PROPERTY_U32 = 3,
    GENERIC_TA_CMD_GET_PROPERTY_U64 = 4,
    GENERIC_TA_CMD_GET_PROPERTY_BINARY = 5,     /* 新增 */
    GENERIC_TA_CMD_GET_PROPERTY_UUID = 6,       /* 新增 */
    GENERIC_TA_CMD_GET_PROPERTY_IDENTITY = 7,   /* 新增 */
    GENERIC_TA_CMD_ENUM_PROPERTIES = 8,         /* 新增 */
    GENERIC_TA_CMD_GET_PROPERTY_NAME = 9,       /* 新增 */
};
```

#### 6.2.2 扩展响应消息

```c
struct property_response {
    int32_t result;                      /* 操作结果 */
    union {
        char str_value[256];             /* 字符串值 */
        bool bool_value;                 /* 布尔值 */
        uint32_t u32_value;              /* 32位整数值 */
        uint64_t u64_value;              /* 64位整数值 */
        struct {                         /* 二进制值 */
            uint8_t data[256];
            size_t length;
        } binary_value;
        TEE_UUID uuid_value;             /* UUID值 */
        TEE_Identity identity_value;     /* 身份值 */
        struct {                         /* 枚举响应 */
            char names[16][64];          /* 属性名称数组 */
            size_t count;                /* 属性数量 */
        } enum_response;
    };
};
```

## 7. 完整实现方案

### 7.1 头文件更新

#### 7.1.1 tee_ta_api.h扩展

```c
#ifndef TEE_TA_API_H
#define TEE_TA_API_H

#include <tee_api_defines.h>
#include <tee_api_types.h>

/* 现有属性获取函数 */
TEE_Result TEE_GetPropertyAsString(TEE_PropSetHandle propsetOrEnumerator,
                                   const char* name, char* valueBuffer,
                                   size_t* valueBufferLen);

TEE_Result TEE_GetPropertyAsBool(TEE_PropSetHandle propsetOrEnumerator,
                                 const char* name, bool* value);

TEE_Result TEE_GetPropertyAsU32(TEE_PropSetHandle propsetOrEnumerator,
                                const char* name, uint32_t* value);

TEE_Result TEE_GetPropertyAsU64(TEE_PropSetHandle propsetOrEnumerator,
                                const char* name, uint64_t* value);

/* 新增属性获取函数 */
TEE_Result TEE_GetPropertyAsBinaryBlock(TEE_PropSetHandle propsetOrEnumerator,
                                        const char* name,
                                        void* valueBuffer,
                                        size_t* valueBufferLen);

TEE_Result TEE_GetPropertyAsUUID(TEE_PropSetHandle propsetOrEnumerator,
                                 const char* name,
                                 TEE_UUID* value);

TEE_Result TEE_GetPropertyAsIdentity(TEE_PropSetHandle propsetOrEnumerator,
                                     const char* name,
                                     TEE_Identity* value);

/* 属性枚举器函数 */
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropertyEnumeratorHandle* enumerator);

void TEE_FreePropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator);

TEE_Result TEE_StartPropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator,
                                       TEE_PropSetHandle propSet);

void TEE_ResetPropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator);

TEE_Result TEE_GetPropertyName(TEE_PropertyEnumeratorHandle enumerator,
                               void* nameBuffer,
                               size_t* nameBufferLen);

TEE_Result TEE_GetNextProperty(TEE_PropertyEnumeratorHandle enumerator);

#endif /* TEE_TA_API_H */
```

### 7.2 核心实现函数

#### 7.2.1 TEE_GetPropertyAsBinaryBlock实现

```c
TEE_Result TEE_GetPropertyAsBinaryBlock(TEE_PropSetHandle propsetOrEnumerator,
                                        const char* name,
                                        void* valueBuffer,
                                        size_t* valueBufferLen) {
    TEE_Result res;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_BINARY;
    uint32_t tmp_len = *valueBufferLen;

    if (!valueBuffer || !valueBufferLen)
        return TEE_ERROR_BAD_PARAMETERS;

    res = propget_get_property(propsetOrEnumerator, name, &type, valueBuffer, &tmp_len);
    if (res != TEE_SUCCESS) {
        if (res == TEE_ERROR_SHORT_BUFFER) {
            *valueBufferLen = tmp_len;
        }
        return res;
    }

    if (type != USER_TA_PROP_TYPE_BINARY)
        return TEE_ERROR_BAD_FORMAT;

    *valueBufferLen = tmp_len;
    return TEE_SUCCESS;
}
```

#### 7.2.2 TEE_GetPropertyAsUUID实现

```c
TEE_Result TEE_GetPropertyAsUUID(TEE_PropSetHandle propsetOrEnumerator,
                                 const char* name,
                                 TEE_UUID* value) {
    TEE_Result res;
    enum user_ta_prop_type type = USER_TA_PROP_TYPE_UUID;
    uint32_t uuid_len = sizeof(TEE_UUID);

    if (!value)
        return TEE_ERROR_BAD_PARAMETERS;

    res = propget_get_property(propsetOrEnumerator, name, &type, value, &uuid_len);
    if (type != USER_TA_PROP_TYPE_UUID)
        res = TEE_ERROR_BAD_FORMAT;

    return res;
}
```

#### 7.2.3 TEE_AllocatePropertyEnumerator实现

```c
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropertyEnumeratorHandle* enumerator) {
    struct property_enumerator* enum_obj;

    if (!enumerator)
        return TEE_ERROR_BAD_PARAMETERS;

    enum_obj = calloc(1, sizeof(*enum_obj));
    if (!enum_obj)
        return TEE_ERROR_OUT_OF_MEMORY;

    list_initialize(&enum_obj->link);
    enum_obj->propset = NULL;
    enum_obj->current_index = 0;
    enum_obj->started = false;
    enum_obj->props = NULL;
    enum_obj->props_count = 0;
    mutex_init(&enum_obj->enum_lock);

    *enumerator = enum_obj;
    return TEE_SUCCESS;
}
```

#### 7.2.4 TEE_StartPropertyEnumerator实现

```c
TEE_Result TEE_StartPropertyEnumerator(TEE_PropertyEnumeratorHandle enumerator,
                                       TEE_PropSetHandle propSet) {
    struct property_enumerator* enum_obj = enumerator;
    const struct user_ta_property* props;
    size_t props_count;
    TEE_Result res;

    if (!enum_obj)
        return TEE_ERROR_BAD_PARAMETERS;

    mutex_acquire(&enum_obj->enum_lock);

    /* 获取属性集对应的属性数组 */
    res = propset_get(propSet, &props, &props_count);
    if (res != TEE_SUCCESS) {
        mutex_release(&enum_obj->enum_lock);
        return res;
    }

    /* 设置枚举器状态 */
    enum_obj->propset = propSet;
    enum_obj->props = props;
    enum_obj->props_count = props_count;
    enum_obj->current_index = 0;
    enum_obj->started = true;

    mutex_release(&enum_obj->enum_lock);
    return TEE_SUCCESS;
}
```

#### 7.2.5 TEE_GetPropertyName实现

```c
TEE_Result TEE_GetPropertyName(TEE_PropertyEnumeratorHandle enumerator,
                               void* nameBuffer,
                               size_t* nameBufferLen) {
    struct property_enumerator* enum_obj = enumerator;
    const char* prop_name;
    size_t name_len;

    if (!enum_obj || !nameBuffer || !nameBufferLen)
        return TEE_ERROR_BAD_PARAMETERS;

    mutex_acquire(&enum_obj->enum_lock);

    if (!enum_obj->started) {
        mutex_release(&enum_obj->enum_lock);
        return TEE_ERROR_BAD_STATE;
    }

    if (enum_obj->current_index >= enum_obj->props_count) {
        mutex_release(&enum_obj->enum_lock);
        return TEE_ERROR_ITEM_NOT_FOUND;
    }

    prop_name = enum_obj->props[enum_obj->current_index].name;
    name_len = strlen(prop_name) + 1;

    if (*nameBufferLen < name_len) {
        *nameBufferLen = name_len;
        mutex_release(&enum_obj->enum_lock);
        return TEE_ERROR_SHORT_BUFFER;
    }

    memcpy(nameBuffer, prop_name, name_len);
    *nameBufferLen = name_len;

    mutex_release(&enum_obj->enum_lock);
    return TEE_SUCCESS;
}
```

## 8. 测试验证方案

### 8.1 单元测试用例

#### 8.1.1 基本属性获取测试

```c
/* 测试字符串属性获取 */
void test_get_property_as_string(void) {
    char buffer[256];
    size_t buffer_len = sizeof(buffer);
    TEE_Result res;

    res = TEE_GetPropertyAsString(TEE_PROPSET_TEE_IMPLEMENTATION,
                                  "gpd.tee.systemTime.protectionLevel",
                                  buffer, &buffer_len);
    assert(res == TEE_SUCCESS);
    assert(strcmp(buffer, "100") == 0);
}

/* 测试U32属性获取 */
void test_get_property_as_u32(void) {
    uint32_t value;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_TEE_IMPLEMENTATION,
                               "gpd.tee.systemTime.protectionLevel",
                               &value);
    assert(res == TEE_SUCCESS);
    assert(value == 100);
}
```

#### 8.1.2 属性枚举测试

```c
/* 测试属性枚举器 */
void test_property_enumerator(void) {
    TEE_PropertyEnumeratorHandle enumerator;
    char name_buffer[64];
    size_t name_len;
    TEE_Result res;

    /* 分配枚举器 */
    res = TEE_AllocatePropertyEnumerator(&enumerator);
    assert(res == TEE_SUCCESS);

    /* 启动枚举 */
    res = TEE_StartPropertyEnumerator(enumerator, TEE_PROPSET_TEE_IMPLEMENTATION);
    assert(res == TEE_SUCCESS);

    /* 枚举属性 */
    while (true) {
        name_len = sizeof(name_buffer);
        res = TEE_GetPropertyName(enumerator, name_buffer, &name_len);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        assert(res == TEE_SUCCESS);

        printf("Property: %s\n", name_buffer);

        res = TEE_GetNextProperty(enumerator);
        if (res == TEE_ERROR_ITEM_NOT_FOUND)
            break;
        assert(res == TEE_SUCCESS);
    }

    /* 释放枚举器 */
    TEE_FreePropertyEnumerator(enumerator);
}
```

### 8.2 集成测试

#### 8.2.1 完整API覆盖测试

测试所有12个GP标准属性API的功能正确性，包括：
- 正常情况下的功能验证
- 边界条件处理
- 错误情况处理
- 并发访问安全性

#### 8.2.2 性能测试

- 属性获取延迟测试
- 枚举器性能测试
- 内存使用效率测试
- 并发访问性能测试

## 9. 总结

### 9.1 实现特点

1. **完整GP标准支持**：实现了全部12个GP标准属性API
2. **向后兼容**：保持现有4个API的完全兼容性
3. **扩展性强**：支持新的属性类型（二进制、UUID、身份）
4. **线程安全**：所有API都支持并发访问
5. **高效实现**：最小化内存使用和通信开销

### 9.2 关键技术优势

- **统一架构**：三层架构清晰，职责分明
- **类型安全**：严格的类型检查和转换
- **错误处理**：完整的错误码体系
- **资源管理**：自动的内存和锁管理
- **可测试性**：完整的测试用例覆盖

### 9.3 后续工作

1. **实现代码编写**：按照设计文档编写具体实现代码
2. **测试用例开发**：开发完整的单元测试和集成测试
3. **性能优化**：根据测试结果进行性能调优
4. **文档完善**：补充用户手册和开发指南

这个设计方案为Trusty TEE提供了完整的GP标准属性API支持，既保持了与现有实现的兼容性，又扩展了新的功能特性，为TA开发者提供了强大而易用的属性管理能力。

## 10. 实施指南

### 10.1 实施优先级

**第一阶段（高优先级）**：
1. 扩展现有数据结构支持新属性类型
2. 实现 `TEE_GetPropertyAsBinaryBlock`
3. 实现 `TEE_GetPropertyAsUUID`
4. 实现 `TEE_GetPropertyAsIdentity`

**第二阶段（中优先级）**：
1. 实现属性枚举器核心功能
2. 实现 `TEE_AllocatePropertyEnumerator`
3. 实现 `TEE_FreePropertyEnumerator`
4. 实现 `TEE_StartPropertyEnumerator`

**第三阶段（标准优先级）**：
1. 实现 `TEE_ResetPropertyEnumerator`
2. 实现 `TEE_GetPropertyName`
3. 实现 `TEE_GetNextProperty`
4. 完善错误处理和边界检查

### 10.2 关键文件修改清单

**需要修改的文件**：
1. `user/base/lib/libutee/include/tee_ta_api.h` - 添加新API声明
2. `user/base/lib/libutee/include/user_ta_header.h` - 扩展属性类型枚举
3. `user/base/lib/libutee/include/tee_api_types.h` - 添加新数据结构
4. `user/base/lib/libutee/tee_api_property.c` - 实现新API函数

**需要新增的文件**：
1. `user/base/lib/libutee/tee_property_enumerator.c` - 枚举器实现
2. `user/base/lib/libutee/include/tee_property_internal.h` - 内部数据结构

### 10.3 测试策略

**单元测试**：
- 每个新API函数的独立测试
- 边界条件和错误情况测试
- 内存泄漏检测

**集成测试**：
- 与现有属性系统的兼容性测试
- 多线程并发访问测试
- 性能基准测试

**回归测试**：
- 确保现有4个API功能不受影响
- 验证现有TA应用的兼容性

### 10.4 质量保证

**代码审查要点**：
1. 内存管理正确性
2. 线程安全性
3. 错误处理完整性
4. GP标准符合性

**性能要求**：
- 属性获取延迟 < 1ms
- 枚举器内存开销 < 1KB
- 支持并发访问数 > 10

### 4.2 TIPC通用TA服务实现（内核侧）

#### 4.2.1 内核属性数据库

```c
/* 内核侧属性数据库 - 扩展OP-TEE的属性集 */
static const struct user_ta_property kernel_tee_props[] = {
    {
        "gpd.tee.arith.maxBigIntSize",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){CFG_TA_BIGNUM_MAX_BITS}
    },
    {
        "gpd.tee.systemTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){100}
    },
    {
        "gpd.tee.TAPersistentTime.protectionLevel",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){1000}
    },
    {
        "gpd.tee.internalCore.version",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){TEE_CORE_API_VERSION}
    },
    /* Trusty特有属性 */
    {
        "trusty.tee.version",
        USER_TA_PROP_TYPE_STRING,
        "1.0.0"
    },
    {
        "trusty.tee.tipc.version",
        USER_TA_PROP_TYPE_U32,
        &(const uint32_t){1}
    }
};

/* 动态客户端属性获取 */
static TEE_Result get_client_properties(uint32_t client_id,
                                         const char* prop_name,
                                         struct property_response* resp) {
    /* 根据客户端ID获取相应属性 */
    if (!strcmp(prop_name, "gpd.client.identity")) {
        resp->prop_type = USER_TA_PROP_TYPE_IDENTITY;
        resp->identity_value.login = TEE_LOGIN_APPLICATION;
        /* 填充客户端UUID */
        return TEE_SUCCESS;
    }

    return TEE_ERROR_ITEM_NOT_FOUND;
}
```

#### 4.2.2 TIPC服务处理函数

```c
/* 处理属性查找请求 */
static int handle_property_request(struct property_request* req,
                                   struct property_response* resp) {
    const struct user_ta_property* props = NULL;
    size_t props_len = 0;
    size_t n;

    /* 根据属性集选择数据源 */
    switch (req->prop_set) {
    case TEE_PROPSET_TEE_IMPLEMENTATION:
        props = kernel_tee_props;
        props_len = ARRAY_SIZE(kernel_tee_props);
        break;
    case TEE_PROPSET_CURRENT_CLIENT:
        return get_client_properties(get_current_client_id(),
                                     req->by_name.name, resp);
    case TEE_PROPSET_CURRENT_TA:
        /* TA属性通常在用户空间处理 */
        return TEE_ERROR_ITEM_NOT_FOUND;
    default:
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 在属性数组中查找 */
    for (n = 0; n < props_len; n++) {
        if (!strcmp(req->by_name.name, props[n].name)) {
            return copy_property_to_response(&props[n], resp);
        }
    }

    return TEE_ERROR_ITEM_NOT_FOUND;
}

/* 处理属性枚举请求 */
static int handle_property_enum_request(struct property_enum_request* req,
                                         struct property_enum_response* resp) {
    const struct user_ta_property* props = NULL;
    size_t props_len = 0;
    size_t start_idx = req->start_index;
    size_t count = 0;
    size_t i;

    /* 获取属性集 */
    switch (req->prop_set) {
    case TEE_PROPSET_TEE_IMPLEMENTATION:
        props = kernel_tee_props;
        props_len = ARRAY_SIZE(kernel_tee_props);
        break;
    case TEE_PROPSET_CURRENT_CLIENT:
        /* 客户端属性动态生成 */
        return enumerate_client_properties(req, resp);
    default:
        return TEE_ERROR_BAD_PARAMETERS;
    }

    resp->total_count = props_len;

    /* 复制属性信息 */
    for (i = start_idx; i < props_len && count < req->count &&
         count < ARRAY_SIZE(resp->properties); i++, count++) {
        strncpy(resp->properties[count].name, props[i].name,
                sizeof(resp->properties[count].name) - 1);
        resp->properties[count].type = props[i].type;
    }

    resp->returned_count = count;
    return TEE_SUCCESS;
}
```

## 5. 文件结构和修改清单（基于OP-TEE属性组织 + TIPC）

### 5.1 头文件

#### 5.1.1 lib/libutee/include/tee_internal_api.h
- GP API函数声明
- 与OP-TEE保持一致的接口定义
- 12个完整的属性API声明

#### 5.1.2 lib/libutee/include/tee_api_types.h
- 基础数据类型定义
- TEE_UUID、TEE_Identity等类型
- TEE_PropSetHandle类型定义

#### 5.1.3 lib/libutee/include/tee_api_defines.h
- 常量定义
- 属性集句柄定义（TEE_PROPSET_*）
- PROP_ENUMERATOR_NOT_STARTED常量

#### 5.1.4 lib/libutee/include/user_ta_header.h
- 属性类型和结构定义
- user_ta_property结构
- user_ta_prop_type枚举

#### 5.1.5 user/base/include/shared/generic_ta_msg.h
- TIPC通信协议定义
- property_request/response结构
- generic_ta_cmd枚举扩展

### 5.2 实现文件

#### 5.2.1 lib/libutee/tee_api_property.c
- GP API主要实现
- 参考OP-TEE的propget_get_property函数
- 本地属性数组 + TIPC服务集成
- 属性枚举器实现

#### 5.2.2 user/base/app/generic_ta/generic_ta.c
- TIPC通用TA服务扩展
- 属性查找和枚举服务实现
- 内核属性数据库管理

#### 5.2.3 lib/libutee/include/tee_property_internal.h (新增)
- 内部数据结构定义
- prop_enumerator结构
- TIPC服务接口声明

#### 5.2.4 lib/libutee/tee_property_data.c (新增)
- 静态属性数组定义
- tee_props[]、ta_props[]数组
- propset_get函数实现

## 6. 实施优先级和计划

### 6.1 第一阶段：基础属性组织
1. 建立OP-TEE风格的属性数组（tee_props[]、ta_props[]）
2. 实现propset_get和propget_get_ext_prop函数
3. 扩展TIPC通用TA服务支持属性查找

### 6.2 第二阶段：完整API支持
1. 实现所有7个属性获取API（基于propget_get_property）
2. 实现5个属性枚举器API
3. 集成本地属性 + TIPC服务的混合查找机制

### 6.3 第三阶段：测试和优化
1. 完整的单元测试和集成测试
2. 性能优化和错误处理完善
3. 与现有TIPC服务的兼容性验证

## 7. 总结

这个基于OP-TEE属性组织的设计方案具有以下特点：

1. **OP-TEE兼容性**：属性组织方式与OP-TEE保持高度一致
2. **TIPC服务保留**：继续使用TIPC通用TA服务获取内核属性
3. **混合查找机制**：本地静态属性 + TIPC动态属性的组合
4. **枚举器支持**：完整的属性枚举机制，支持跨本地和远程属性
5. **简化实现**：专注于属性获取，不考虑manifest属性
6. **完整GP支持**：实现全部12个GP标准属性API

### 7.1 关键优势

- **保持现有架构**：不需要引入系统调用，保留TIPC服务机制
- **OP-TEE兼容**：属性分类、枚举方式与OP-TEE一致
- **扩展性强**：支持通过TIPC服务动态扩展属性
- **实现简单**：基于现有TIPC基础设施，开发工作量较小

### 7.2 核心设计要点

1. **三类属性集**：TEE_IMPLEMENTATION、CURRENT_TA、CURRENT_CLIENT
2. **本地优先**：优先查找本地静态属性数组
3. **TIPC扩展**：本地未找到时通过TIPC服务查找
4. **枚举器状态**：基于索引的枚举机制，支持跨数据源
5. **类型安全**：严格的属性类型检查和转换

这个设计方案为Trusty TEE提供了与OP-TEE兼容的属性系统，既保持了GP标准的完整性，又充分利用了Trusty现有的TIPC服务基础设施，为后续的属性系统扩展提供了灵活的架构基础。
